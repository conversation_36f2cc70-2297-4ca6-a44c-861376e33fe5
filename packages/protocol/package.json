{"name": "@mcpkit/protocol", "version": "0.0.0-dev", "description": "Protocol bridge layer for MCP TypeScript SDK integration", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js"}, "scripts": {"build": "tsc -p tsconfig.build.json", "test": "vitest run", "test:ci": "vitest run --coverage", "test:types": "echo 'No type definition tests for this package'", "lint": "npx @biomejs/biome check src", "typecheck": "tsc -p . --noEmit"}, "keywords": ["mcpkit", "protocol", "mcp", "bridge"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/GordonDrop/mcpkit.git", "directory": "packages/protocol"}, "bugs": {"url": "https://github.com/GordonDrop/mcpkit/issues"}, "homepage": "https://github.com/GordonDrop/mcpkit/tree/main/packages/protocol#readme", "dependencies": {"@modelcontextprotocol/sdk": "github:modelcontextprotocol/typescript-sdk#c40193933a43b61d40e616b7ab513e87b3d978de", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.0.0", "@vitest/coverage-v8": "1.6.0", "typescript": "^5.0.0", "vitest": "^1.0.0"}}