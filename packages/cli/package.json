{"name": "@mcpkit/cli", "version": "0.1.1", "description": "CLI tools for McpKit development and validation", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "bin": {"mcp": "./dist/index.js"}, "exports": {".": "./dist/index.js"}, "scripts": {"build": "tsc -p tsconfig.build.json", "test": "vitest run", "test:ci": "vitest run --coverage", "test:types": "echo 'No type definition tests for this package'", "lint": "npx @biomejs/biome check src", "typecheck": "tsc -p . --noEmit"}, "keywords": ["mcpkit", "cli", "development", "validation", "mcp"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/GordonDrop/mcpkit.git", "directory": "packages/cli"}, "bugs": {"url": "https://github.com/GordonDrop/mcpkit/issues"}, "homepage": "https://github.com/GordonDrop/mcpkit/tree/main/packages/cli#readme", "peerDependencies": {"@mcpkit/server": "^0.1.0"}, "dependencies": {"@mcpkit/core": "workspace:^", "chalk": "^5.3.0", "chokidar": "^3.5.3", "commander": "^12.0.0", "deep-diff": "^1.0.2", "pretty-ms": "^8.0.0", "tsx": "^4.20.3", "zod": "^3.22.4"}, "devDependencies": {"@mcpkit/server": "workspace:^", "@types/deep-diff": "^1.0.2", "@types/node": "^20.0.0", "@vitest/coverage-v8": "1.6.0", "execa": "^8.0.1", "typescript": "^5.0.0", "vitest": "^1.0.0"}}