{"name": "@mcpkit/core", "version": "0.0.2", "description": "Core components for MCPKit", "keywords": ["mcpkit", "core"], "license": "MIT", "author": "<PERSON>", "repository": {"type": "git", "url": "https://github.com/GordonDrop/mcpkit.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/GordonDrop/mcpkit/issues"}, "homepage": "https://github.com/GordonDrop/mcpkit/tree/main/packages/core#readme", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts", "test": "vitest run", "test:ci": "vitest run --coverage", "test:types": "tsd", "lint": "npx @biomejs/biome check src", "typecheck": "tsc -p . --noEmit"}, "peerDependencies": {"zod": "^3.22.4"}, "devDependencies": {"@vitest/coverage-v8": "1.6.0", "tsup": "^8.0.2"}, "dependencies": {"@mcpkit/protocol": "workspace:*", "zod-to-json-schema": "^3.24.5"}, "tsd": {"directory": "tests"}}