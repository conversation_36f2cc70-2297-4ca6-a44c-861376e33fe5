{"name": "@mcpkit/transport-stdio", "version": "0.0.2", "description": "Stdio transport implementation for MCP servers", "type": "module", "main": "./dist/index.js", "exports": {".": "./dist/index.js"}, "scripts": {"build": "tsc -p tsconfig.build.json", "test": "vitest run", "test:ci": "vitest run", "test:types": "echo 'No type definition tests for this package'", "lint": "npx @biomejs/biome check src", "typecheck": "tsc -p . --noEmit"}, "keywords": ["mcp", "transport", "stdio", "json-rpc"], "author": "<PERSON>", "license": "ISC", "repository": {"type": "git", "url": "https://github.com/GordonDrop/mcpkit.git", "directory": "packages/transport-stdio"}, "bugs": {"url": "https://github.com/GordonDrop/mcpkit/issues"}, "homepage": "https://github.com/GordonDrop/mcpkit/tree/main/packages/transport-stdio#readme", "peerDependencies": {"@mcpkit/server": "workspace:*"}, "dependencies": {"@mcpkit/ndjson": "workspace:*"}, "devDependencies": {"typescript": "^5.0.0", "vitest": "^1.0.0", "@types/node": "^20.0.0", "@mcpkit/core": "workspace:*"}}