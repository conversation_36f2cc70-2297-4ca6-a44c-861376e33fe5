{"name": "@mcpkit/server", "version": "0.1.0", "description": "Chainable builder API for creating MCP servers", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js"}, "scripts": {"build": "tsc -p tsconfig.build.json", "test": "vitest run", "test:ci": "vitest run", "test:types": "echo 'No type definition tests for this package'", "lint": "npx @biomejs/biome check src", "typecheck": "tsc -p . --noEmit"}, "keywords": ["mcpkit", "server", "builder", "mcp"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/GordonDrop/mcpkit.git", "directory": "packages/server"}, "bugs": {"url": "https://github.com/GordonDrop/mcpkit/issues"}, "homepage": "https://github.com/GordonDrop/mcpkit/tree/main/packages/server#readme", "peerDependencies": {"@mcpkit/core": "workspace:^"}, "dependencies": {"@mcpkit/transport-stdio": "workspace:^"}, "devDependencies": {"@mcpkit/core": "workspace:^", "typescript": "^5.0.0", "vitest": "^1.0.0", "zod": "^3.22.4"}}