{"$schema": "https://turbo.build/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**"], "cache": true}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"], "cache": true}, "test:ci": {"dependsOn": ["^build"], "outputs": ["coverage/**"], "cache": true}, "lint": {"outputs": [], "cache": true}, "typecheck": {"outputs": [], "cache": true}, "test:types": {"dependsOn": ["build"], "outputs": [], "cache": true}}}