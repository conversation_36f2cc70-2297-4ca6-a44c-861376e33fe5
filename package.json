{"name": "mcpkit-monorepo", "private": true, "packageManager": "pnpm@9.1.0", "version": "0.1.0", "workspaces": ["packages/*", "examples/*"], "scripts": {"bootstrap": "pnpm install", "build": "turbo run build", "test": "turbo run test --", "test:ci": "turbo run test:ci --", "test:types": "turbo run test:types", "lint": "turbo run lint", "lint:all": "npx @biomejs/biome check .", "lint:fix": "npx @biomejs/biome check --write .", "format": "npx @biomejs/biome format --write .", "typecheck": "turbo run typecheck", "ci": "pnpm bootstrap && pnpm lint && pnpm typecheck && pnpm test", "prepare": "husky install", "changeset": "changeset", "version": "npx changeset version", "changelog": "npx changeset version"}, "devDependencies": {"@biomejs/biome": "^2.0.2", "@changesets/cli": "^2.29.5", "@types/node": "^20.12.0", "husky": "^8.0.0", "lint-staged": "^15.0.0", "tsd": "^0.32.0", "turbo": "^2.0.0", "typescript": "^5.5.0", "vitest": "^1.6.1"}, "lint-staged": {"*.{ts,tsx,js}": ["npx @biomejs/biome check --write"]}, "pnpm": {"overrides": {"@modelcontextprotocol/sdk": "1.13.2"}}}